import { createApp } from 'vue'
import App from './App.vue'
import { createI18n } from 'vue-i18n'
import naive from 'naive-ui'

// 语言包
const messages = {
  zh: {
    nav: {
      home: '首页',
      intro: '介绍',
      future: '未来',
      contact: '联系',
      forum: '论坛',
      hayfrp: 'HayFrp'
    },
    home: {
      title: '你好，空旷网络',
      greeting: 'Hello KongKuang Network'
    },
    intro: {
      title: '关于空旷网络',
      subtitle: '致力于构建开放、创新的网络生态',
      description: '空旷网络成立于2024年，专注于网络技术创新与发展。\n我们致力于为用户提供高质量的网络服务和解决方案。\n通过技术创新，让网络连接更加便捷、安全、高效。\n推动互联网基础设施的持续发展和完善。\n为数字化时代的发展贡献我们的力量。',
      mission: {
        title: '我们的使命',
        content: '通过技术创新，让网络连接更加便捷、安全、高效，为数字化时代的发展贡献力量。'
      },
      vision: {
        title: '我们的愿景',
        content: '成为网络技术领域的领先企业，推动互联网基础设施的持续发展和完善。'
      },
      values: {
        title: '核心价值观',
        items: ['创新驱动', '用户至上', '开放合作', '持续学习']
      }
    },
    theme: {
      light: '浅色',
      dark: '深色'
    }
  },
  en: {
    nav: {
      home: 'Home',
      intro: 'Introduction',
      future: 'Future',
      contact: 'Contact',
      forum: 'Forum',
      hayfrp: 'HayFrp'
    },
    home: {
      title: 'Hello, Kong Kuang Network',
      greeting: 'Hello Kong Kuang Network'
    },
    intro: {
      title: 'About Kong Kuang Network',
      subtitle: 'Committed to building an open and innovative network ecosystem',
      description: 'Kong Kuang Network was founded in 2024, focusing on network technology innovation and development.\nWe are committed to providing users with high-quality network services and solutions.\nThrough technological innovation, we make network connections more convenient, secure, and efficient.\nPromoting the continuous development and improvement of Internet infrastructure.\nContributing our strength to the development of the digital age.',
      mission: {
        title: 'Our Mission',
        content: 'Through technological innovation, make network connections more convenient, secure, and efficient, contributing to the development of the digital age.'
      },
      vision: {
        title: 'Our Vision',
        content: 'To become a leading enterprise in the field of network technology, promoting the continuous development and improvement of Internet infrastructure.'
      },
      values: {
        title: 'Core Values',
        items: ['Innovation Driven', 'User First', 'Open Cooperation', 'Continuous Learning']
      }
    },
    theme: {
      light: 'Light',
      dark: 'Dark'
    }
  }
}

// 创建i18n实例
const i18n = createI18n({
  locale: 'zh', // 默认语言
  fallbackLocale: 'en',
  messages,
  legacy: false, // 使用 Composition API 模式
  globalInjection: true
})

const app = createApp(App)
app.use(naive)
app.use(i18n)
app.mount('#app')
