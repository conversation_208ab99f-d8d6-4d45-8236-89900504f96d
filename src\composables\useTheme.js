import { ref, computed } from 'vue'
import { darkTheme } from 'naive-ui'

// 主题状态
const isDark = ref(false)

// 淡蓝色主题配置
const lightBlueTheme = {
  common: {
    primaryColor: '#60a5fa',
    primaryColorHover: '#3b82f6',
    primaryColorPressed: '#2563eb',
    primaryColorSuppl: '#93c5fd',
    infoColor: '#60a5fa',
    successColor: '#34d399',
    warningColor: '#fbbf24',
    errorColor: '#f87171',
    textColorBase: '#1e293b',
    textColor1: '#0f172a',
    textColor2: '#334155',
    textColor3: '#64748b',
    dividerColor: '#e2e8f0',
    borderColor: '#cbd5e1',
    tableHeaderColor: '#f1f5f9',
    hoverColor: '#f8fafc',
    cardColor: '#ffffff',
    modalColor: '#ffffff',
    bodyColor: '#f0f9ff',
    tagColor: '#dbeafe',
    avatarColor: '#bfdbfe',
    invertedColor: '#1e293b',
    baseColor: '#ffffff',
    railColor: '#e2e8f0',
    actionColor: '#f0f9ff'
  }
}

// 背景图片配置
const backgroundImages = {
  light: 'https://image.kongkuang.top/index/background/background-1-light.jpg',
  dark: 'https://image.kongkuang.top/index/background/background-1-dark.jpg'
}

export function useTheme() {
  // 计算当前主题
  const theme = computed(() => {
    if (isDark.value) {
      return darkTheme
    } else {
      return {
        ...lightBlueTheme,
        name: 'lightBlue'
      }
    }
  })
  
  // 计算当前背景图片
  const backgroundImage = computed(() => 
    isDark.value ? backgroundImages.dark : backgroundImages.light
  )
  
  // 切换主题
  const toggleTheme = () => {
    isDark.value = !isDark.value
    // 保存到localStorage
    localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
  }
  
  // 初始化主题
  const initTheme = () => {
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme) {
      isDark.value = savedTheme === 'dark'
    } else {
      // 检测系统主题偏好
      isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    }
  }
  
  return {
    isDark,
    theme,
    backgroundImage,
    toggleTheme,
    initTheme
  }
}
