<template>
  <div class="intro-page">
    <div class="intro-content">
      <div class="intro-header">
        <h1 class="intro-title">{{ $t('intro.title') }}</h1>
        <p class="intro-subtitle">{{ $t('intro.subtitle') }}</p>
      </div>

      <div class="intro-body">
        <div class="intro-description">
          <p
            v-for="(line, index) in descriptionLines"
            :key="index"
            class="description-line"
          >
            {{ line }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// 将描述文本按换行符分割成数组
const descriptionLines = computed(() => {
  return t('intro.description').split('\n').filter(line => line.trim())
})
</script>

<style scoped>
.intro-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80px 24px 24px;
  color: white;
}

.intro-content {
  max-width: 1200px;
  width: 100%;
  text-align: center;
}

.intro-header {
  margin-bottom: 60px;
}

.intro-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: 24px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.intro-subtitle {
  font-size: clamp(1.2rem, 2.5vw, 1.8rem);
  font-weight: 400;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
}

.intro-body {
  text-align: center;
}

.intro-description {
  font-size: clamp(1rem, 2vw, 1.3rem);
  line-height: 1.8;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
}

.description-line {
  margin-bottom: 1rem;
  opacity: 0.95;
  transition: opacity 0.3s ease;
}

.description-line:hover {
  opacity: 1;
}

.description-line:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .intro-page {
    padding: 80px 16px 24px;
  }
}
</style>
