<template>
  <div class="navbar">
    <div class="navbar-content">
      <!-- Logo -->
      <div class="logo">
        <img src="https://ico.kongkuang.top/kongkuangnetwork-logo.png" alt="空旷网络" class="logo-icon">
        <span class="logo-text">空旷网络</span>
      </div>
      
      <!-- 导航菜单 -->
      <div class="nav-menu">
        <div class="nav-items">
          <a @click="goToPage('home')" class="nav-item" :class="{ active: currentPage === 'home' }">{{ $t('nav.home') }}</a>
          <a @click="goToPage('intro')" class="nav-item" :class="{ active: currentPage === 'intro' }">{{ $t('nav.intro') }}</a>
          <a @click="goToPage('future')" class="nav-item" :class="{ active: currentPage === 'future' }">{{ $t('nav.future') }}</a>
          <a @click="goToPage('contact')" class="nav-item" :class="{ active: currentPage === 'contact' }">{{ $t('nav.contact') }}</a>
          <a @click="goToPage('forum')" class="nav-item" :class="{ active: currentPage === 'forum' }">{{ $t('nav.forum') }}</a>
          <a @click="goToPage('hayfrp')" class="nav-item" :class="{ active: currentPage === 'hayfrp' }">{{ $t('nav.hayfrp') }}</a>
        </div>
      </div>
      
      <!-- 右侧控制 -->
      <div class="nav-controls">
        <div class="controls-wrapper">
          <!-- 语言切换 -->
          <div class="language-switch">
            <n-icon class="language-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03A17.52 17.52 0 0 0 14.07 6H17V4h-7V2H8v2H1v2h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
              </svg>
            </n-icon>
            <n-dropdown :options="languageOptions" @select="handleLanguageSelect">
              <span class="language-text">{{ currentLanguage }}</span>
            </n-dropdown>
          </div>

          <!-- 主题切换 -->
          <div class="theme-switch">
            <n-icon class="theme-icon sun-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 7a5 5 0 0 1 5 5a5 5 0 0 1-5 5a5 5 0 0 1-5-5a5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3a3 3 0 0 0 3 3a3 3 0 0 0 3-3a3 3 0 0 0-3-3m0-7l2.39 3.42C13.65 5.15 12.84 5 12 5c-.84 0-1.65.15-2.39.42L12 2M3.34 7l4.16-.35A7.2 7.2 0 0 0 5.94 8.5c-.44.74-.69 1.5-.83 2.29L3.34 7m.02 10l1.76-3.77a7.131 7.131 0 0 0 2.38 4.14L3.36 17M20.65 7l-1.77 3.79a7.023 7.023 0 0 0-2.38-4.15l4.15.36m-.01 10l-4.14.36c.59-.51 1.12-1.14 1.54-1.86c.42-.73.69-1.5.83-2.29L20.64 17M12 22l-2.41-3.44c.74.27 1.55.44 2.41.44c.82 0 1.63-.17 2.37-.44L12 22z"/>
              </svg>
            </n-icon>
            <div class="switch-container">
              <n-switch
                v-model:value="isDark"
                @update:value="handleThemeToggle"
                size="medium"
                :rail-style="railStyle"
              />
              <span class="switch-text">{{ isDark ? $t('theme.dark') : $t('theme.light') }}</span>
            </div>
            <n-icon class="theme-icon moon-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 18c-.89 0-1.74-.19-2.5-.54C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.46C10.26 6.19 11.11 6 12 6a6 6 0 0 1 6 6a6 6 0 0 1-6 6z"/>
              </svg>
            </n-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { NIcon, NDropdown, NSwitch } from 'naive-ui'
import { useTheme } from '../composables/useTheme'
import { usePage } from '../composables/usePage'

const { locale } = useI18n()
const { isDark } = useTheme()
const { currentPage, goToPage } = usePage()

// 当前语言显示
const currentLanguage = computed(() => {
  return locale.value === 'zh' ? '中文' : 'English'
})

// 语言选项
const languageOptions = [
  {
    label: '中文',
    key: 'zh'
  },
  {
    label: 'English',
    key: 'en'
  }
]

// 处理语言切换
const handleLanguageSelect = (key) => {
  locale.value = key
  localStorage.setItem('language', key)
}

// 处理主题切换
const handleThemeToggle = () => {
  // 保存到localStorage
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

// Switch样式
const railStyle = ({ focused, checked }) => {
  const style = {}
  if (checked) {
    style.background = '#60a5fa'
  } else {
    style.background = '#93c5fd'
  }
  if (focused) {
    style.boxShadow = '0 0 0 2px rgba(96, 165, 250, 0.3)'
  }
  return style
}
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: transparent;
}

.navbar-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
  font-weight: 600;
}

.logo-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: contain;
}

.logo-text {
  font-size: 18px;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-items {
  display: flex;
  gap: 32px;
}

.nav-item {
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
}

.nav-item:hover {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.nav-item.active {
  color: white;
  background: rgba(96, 165, 250, 0.3);
  border: 1px solid rgba(96, 165, 250, 0.4);
}

.nav-controls {
  color: white;
}

.controls-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.language-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.9);
}

.language-icon {
  font-size: 16px;
}

.language-text {
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.language-text:hover {
  color: white;
}

.theme-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 24px;
  background: rgba(96, 165, 250, 0.25);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(96, 165, 250, 0.4);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-text {
  font-size: 13px;
  color: white;
  font-weight: 500;
  min-width: 32px;
  text-align: center;
}

.theme-icon {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.sun-icon {
  color: rgba(255, 255, 255, 0.6);
}

.moon-icon {
  color: rgba(255, 255, 255, 0.9);
}

.theme-switch:hover .theme-icon {
  color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar-content {
    padding: 0 16px;
  }
  
  .nav-menu {
    display: none;
  }
  
  .logo-text {
    display: none;
  }
}
</style>
