<template>
  <div class="navbar">
    <div class="navbar-content">
      <!-- Logo -->
      <div class="logo">
        <img src="https://ico.kongkuang.top/kongkuangnetwork-logo.png" alt="空旷网络" class="logo-icon">
        <span class="logo-text">空旷网络</span>
      </div>
      
      <!-- 导航菜单 -->
      <div class="nav-menu">
        <div class="nav-items">
          <a @click="goToPage('home')" class="nav-item" :class="{ active: currentPage === 'home' }">{{ $t('nav.home') }}</a>
          <a @click="goToPage('intro')" class="nav-item" :class="{ active: currentPage === 'intro' }">{{ $t('nav.intro') }}</a>
          <a @click="goToPage('future')" class="nav-item" :class="{ active: currentPage === 'future' }">{{ $t('nav.future') }}</a>
          <a @click="goToPage('contact')" class="nav-item" :class="{ active: currentPage === 'contact' }">{{ $t('nav.contact') }}</a>
          <a @click="goToPage('forum')" class="nav-item" :class="{ active: currentPage === 'forum' }">{{ $t('nav.forum') }}</a>
          <a @click="goToPage('hayfrp')" class="nav-item" :class="{ active: currentPage === 'hayfrp' }">{{ $t('nav.hayfrp') }}</a>
        </div>
      </div>
      
      <!-- 右侧控制 -->
      <div class="nav-controls">
        <div class="controls-wrapper">
          <!-- 语言切换 -->
          <div class="language-switch">
            <n-icon class="language-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03A17.52 17.52 0 0 0 14.07 6H17V4h-7V2H8v2H1v2h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/>
              </svg>
            </n-icon>
            <n-dropdown
              :options="languageOptions"
              @select="handleLanguageSelect"
              :render-label="renderDropdownLabel"
              placement="bottom"
              trigger="click"
              :show-arrow="false"
              :animated="true"
            >
              <span class="language-text">{{ currentLanguage }}</span>
            </n-dropdown>
          </div>

          <!-- 主题切换 -->
          <div class="theme-switch">
            <n-icon class="theme-icon sun-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 7a5 5 0 0 1 5 5a5 5 0 0 1-5 5a5 5 0 0 1-5-5a5 5 0 0 1 5-5m0 2a3 3 0 0 0-3 3a3 3 0 0 0 3 3a3 3 0 0 0 3-3a3 3 0 0 0-3-3m0-7l2.39 3.42C13.65 5.15 12.84 5 12 5c-.84 0-1.65.15-2.39.42L12 2M3.34 7l4.16-.35A7.2 7.2 0 0 0 5.94 8.5c-.44.74-.69 1.5-.83 2.29L3.34 7m.02 10l1.76-3.77a7.131 7.131 0 0 0 2.38 4.14L3.36 17M20.65 7l-1.77 3.79a7.023 7.023 0 0 0-2.38-4.15l4.15.36m-.01 10l-4.14.36c.59-.51 1.12-1.14 1.54-1.86c.42-.73.69-1.5.83-2.29L20.64 17M12 22l-2.41-3.44c.74.27 1.55.44 2.41.44c.82 0 1.63-.17 2.37-.44L12 22z"/>
              </svg>
            </n-icon>
            <div class="switch-container">
              <n-switch
                v-model:value="isDark"
                @update:value="handleThemeToggle"
                size="medium"
                :rail-style="railStyle"
              />
              <span class="switch-text">{{ isDark ? $t('theme.dark') : $t('theme.light') }}</span>
            </div>
            <n-icon class="theme-icon moon-icon">
              <svg viewBox="0 0 24 24">
                <path fill="currentColor" d="M12 18c-.89 0-1.74-.19-2.5-.54C11.56 16.5 13 14.42 13 12s-1.44-4.5-3.5-5.46C10.26 6.19 11.11 6 12 6a6 6 0 0 1 6 6a6 6 0 0 1-6 6z"/>
              </svg>
            </n-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { NIcon, NDropdown, NSwitch } from 'naive-ui'
import { useTheme } from '../composables/useTheme'
import { usePage } from '../composables/usePage'

const { locale } = useI18n()
const { isDark } = useTheme()
const { currentPage, goToPage } = usePage()

// 当前语言显示
const currentLanguage = computed(() => {
  return locale.value === 'zh' ? '中文' : 'English'
})

// 语言选项
const languageOptions = [
  {
    label: '中文',
    key: 'zh',
    icon: '🇨🇳'
  },
  {
    label: 'English',
    key: 'en',
    icon: '🇺🇸'
  }
]

// 渲染下拉选项
const renderDropdownLabel = (option) => {
  return `${option.icon} ${option.label}`
}

// 下拉菜单样式
const dropdownStyle = {
  background: 'rgba(173, 216, 230, 0.95)',
  backdropFilter: 'blur(20px)',
  borderRadius: '12px',
  border: '1px solid rgba(255, 255, 255, 0.3)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
}

// 处理语言切换
const handleLanguageSelect = (key) => {
  locale.value = key
  localStorage.setItem('language', key)
}

// 处理主题切换
const handleThemeToggle = () => {
  // 保存到localStorage
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

// Switch样式
const railStyle = ({ focused, checked }) => {
  const style = {}
  if (checked) {
    style.background = '#1e293b'
  } else {
    style.background = 'rgba(255, 255, 255, 0.4)'
  }
  if (focused) {
    style.boxShadow = '0 0 0 2px rgba(30, 41, 59, 0.3)'
  }
  return style
}
</script>

<style scoped>
.navbar {
  position: fixed;
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: calc(100% - 32px);
  max-width: 1200px;
  background: rgba(135, 206, 235, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.navbar-content {
  padding: 0 32px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #1e293b;
  font-weight: 700;
}

.logo-icon {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  object-fit: contain;
}

.logo-text {
  font-size: 18px;
  font-weight: 700;
}

.nav-menu {
  flex: 1;
  display: flex;
  justify-content: center;
}

.nav-items {
  display: flex;
  gap: 8px;
  background: rgba(255, 255, 255, 0.15);
  padding: 6px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
}

.nav-item {
  color: #1e293b;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  padding: 10px 20px;
  border-radius: 16px;
  cursor: pointer;
  white-space: nowrap;
}

.nav-item:hover {
  color: #0f172a;
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
}

.nav-item.active {
  color: #0f172a;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-controls {
  color: #1e293b;
}

.controls-wrapper {
  display: flex;
  align-items: center;
  gap: 16px;
}

.language-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #1e293b;
  padding: 8px 16px;
  border-radius: 16px;
  background: rgba(173, 216, 230, 0.6);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.language-switch:hover {
  background: rgba(173, 216, 230, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.4);
}

.language-icon {
  font-size: 16px;
}

.language-text {
  font-size: 14px;
  cursor: pointer;
  font-weight: 600;
  transition: color 0.3s ease;
}

.language-text:hover {
  color: #0f172a;
}

.theme-switch {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 16px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.theme-switch:hover {
  background: rgba(255, 255, 255, 0.25);
}

.switch-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.switch-text {
  font-size: 13px;
  color: #1e293b;
  font-weight: 600;
  min-width: 32px;
  text-align: center;
}

.theme-icon {
  font-size: 16px;
  color: #475569;
  transition: all 0.3s ease;
}

.sun-icon {
  color: #64748b;
}

.moon-icon {
  color: #334155;
}

.theme-switch:hover .theme-icon {
  color: #1e293b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .navbar {
    top: 8px;
    width: calc(100% - 16px);
  }

  .navbar-content {
    padding: 0 16px;
    height: 48px;
  }

  .nav-items {
    gap: 4px;
    padding: 4px;
  }

  .nav-item {
    padding: 8px 12px;
    font-size: 13px;
  }

  .logo-text {
    display: none;
  }

  .controls-wrapper {
    gap: 8px;
  }

  .language-switch,
  .theme-switch {
    padding: 6px 12px;
  }
}

@media (max-width: 480px) {
  .nav-menu {
    display: none;
  }
}

/* 语言下拉菜单样式 */
:deep(.n-dropdown-menu) {
  background: rgba(173, 216, 230, 0.95) !important;
  backdrop-filter: blur(20px);
  border-radius: 12px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  padding: 8px !important;
}

:deep(.n-dropdown-option) {
  border-radius: 8px !important;
  margin: 2px 0 !important;
  color: #1e293b !important;
  font-weight: 600 !important;
}

:deep(.n-dropdown-option:hover) {
  background: rgba(255, 255, 255, 0.4) !important;
  color: #0f172a !important;
}

:deep(.n-dropdown-option.n-dropdown-option--selected) {
  background: rgba(255, 255, 255, 0.6) !important;
  color: #0f172a !important;
}
</style>

<!-- 全局样式用于下拉框 -->
<style>
/* 语言切换下拉框样式 */
.n-dropdown-menu {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px) !important;
  border-radius: 16px !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
  padding: 8px !important;
  min-width: 120px !important;
}

.n-dropdown-option {
  border-radius: 12px !important;
  margin: 2px 0 !important;
  padding: 10px 16px !important;
  color: #1e293b !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
}

.n-dropdown-option:hover {
  background: rgba(135, 206, 235, 0.3) !important;
  color: #0f172a !important;
}

.n-dropdown-option--selected {
  background: rgba(135, 206, 235, 0.5) !important;
  color: #0f172a !important;
}

.n-dropdown-option--selected:hover {
  background: rgba(135, 206, 235, 0.6) !important;
}
</style>
