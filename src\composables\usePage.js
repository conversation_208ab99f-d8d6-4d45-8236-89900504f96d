import { ref, computed } from 'vue'

// 页面列表
const pages = ['home', 'intro', 'future', 'contact', 'forum', 'hayfrp']

// 当前页面索引
const currentPageIndex = ref(0)

// 动画状态
const isAnimating = ref(false)

export function usePage() {
  // 当前页面
  const currentPage = computed(() => pages[currentPageIndex.value])
  
  // 是否是第一页
  const isFirstPage = computed(() => currentPageIndex.value === 0)
  
  // 是否是最后一页
  const isLastPage = computed(() => currentPageIndex.value === pages.length - 1)
  
  // 上一页
  const prevPage = () => {
    if (!isFirstPage.value && !isAnimating.value) {
      isAnimating.value = true
      currentPageIndex.value--
      setTimeout(() => {
        isAnimating.value = false
      }, 800) // 与CSS动画时间匹配
    }
  }

  // 下一页
  const nextPage = () => {
    if (!isLastPage.value && !isAnimating.value) {
      isAnimating.value = true
      currentPageIndex.value++
      setTimeout(() => {
        isAnimating.value = false
      }, 800) // 与CSS动画时间匹配
    }
  }
  
  // 跳转到指定页面
  const goToPage = (pageName) => {
    const index = pages.indexOf(pageName)
    if (index !== -1) {
      currentPageIndex.value = index
    }
  }
  
  // 键盘事件处理
  const handleKeydown = (event) => {
    // 如果正在动画中，忽略键盘事件
    if (isAnimating.value) {
      return
    }

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault()
        prevPage()
        break
      case 'ArrowDown':
        event.preventDefault()
        nextPage()
        break
    }
  }

  // 滚轮事件处理
  const handleWheel = (event) => {
    event.preventDefault()

    // 如果正在动画中，忽略滚轮事件
    if (isAnimating.value) {
      return
    }

    // 防抖处理，减少触发频率
    if (handleWheel.timeout) {
      clearTimeout(handleWheel.timeout)
    }

    handleWheel.timeout = setTimeout(() => {
      // 增加滚动阈值，避免过于敏感
      const threshold = 50
      if (event.deltaY > threshold) {
        // 向下滚动，下一页
        nextPage()
      } else if (event.deltaY < -threshold) {
        // 向上滚动，上一页
        prevPage()
      }
    }, 150) // 增加防抖时间
  }
  
  return {
    currentPage,
    currentPageIndex,
    isFirstPage,
    isLastPage,
    prevPage,
    nextPage,
    goToPage,
    handleKeydown,
    handleWheel,
    pages
  }
}
