<template>
  <n-config-provider :theme="theme">
    <div id="app">
      <!-- 导航栏 -->
      <NavBar />

      <!-- 主要内容 -->
      <main class="main-content" :style="{ backgroundImage: `url(${backgroundImage})` }">
        <div class="pages-container" :style="{ transform: `translateY(-${currentPageIndex * 100}vh)` }">
          <!-- 首页 -->
          <div class="page-section">
            <div class="hero-section">
              <div class="hero-content">
                <h1 class="hero-title">
                  {{ $t('home.title') }}！
                </h1>
              </div>
            </div>
          </div>

          <!-- 介绍页 -->
          <div class="page-section">
            <IntroPage />
          </div>

          <!-- 未来页 -->
          <div class="page-section">
            <div class="page-placeholder">
              <h1 class="placeholder-title">{{ $t('nav.future') }}</h1>
              <p class="placeholder-text">页面开发中...</p>
            </div>
          </div>

          <!-- 联系页 -->
          <div class="page-section">
            <div class="page-placeholder">
              <h1 class="placeholder-title">{{ $t('nav.contact') }}</h1>
              <p class="placeholder-text">页面开发中...</p>
            </div>
          </div>

          <!-- 论坛页 -->
          <div class="page-section">
            <div class="page-placeholder">
              <h1 class="placeholder-title">{{ $t('nav.forum') }}</h1>
              <p class="placeholder-text">页面开发中...</p>
            </div>
          </div>

          <!-- HayFrp页 -->
          <div class="page-section">
            <div class="page-placeholder">
              <h1 class="placeholder-title">{{ $t('nav.hayfrp') }}</h1>
              <p class="placeholder-text">页面开发中...</p>
            </div>
          </div>
        </div>

        <!-- 页面进度指示器 -->
        <div class="page-progress">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ height: `${((currentPageIndex + 1) / pages.length) * 100}%` }"
            ></div>
          </div>
          <div class="page-counter">
            {{ currentPageIndex + 1 }} / {{ pages.length }}
          </div>
        </div>
      </main>


    </div>
  </n-config-provider>
</template>

<script setup>
import { onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { NConfigProvider } from 'naive-ui'
import NavBar from './components/NavBar.vue'
import IntroPage from './components/IntroPage.vue'
import { useTheme } from './composables/useTheme'
import { usePage } from './composables/usePage'

const { locale } = useI18n()
const { theme, backgroundImage, initTheme } = useTheme()
const {
  currentPage,
  currentPageIndex,
  handleKeydown,
  handleWheel,
  pages
} = usePage()

onMounted(() => {
  // 初始化主题
  initTheme()

  // 初始化语言
  const savedLanguage = localStorage.getItem('language')
  if (savedLanguage) {
    locale.value = savedLanguage
  }

  // 添加键盘和滚轮事件监听
  document.addEventListener('keydown', handleKeydown)
  document.addEventListener('wheel', handleWheel, { passive: false })
})

onUnmounted(() => {
  // 移除键盘和滚轮事件监听
  document.removeEventListener('keydown', handleKeydown)
  document.removeEventListener('wheel', handleWheel)
})
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segui UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
  min-height: 100vh;
}

#app {
  min-height: 100vh;
}

.main-content {
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  position: relative;
  overflow: hidden;
}

.pages-container {
  height: 100vh;
  transition: transform 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform;
}

.page-section {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  opacity: 1;
  transition: opacity 0.3s ease-in-out;
}

/* 页面内容动画 */
.hero-section,
.page-placeholder {
  animation: fadeInUp 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 页面进度指示器 */
.page-progress {
  position: fixed;
  right: 32px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.progress-bar {
  width: 4px;
  height: 120px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  width: 100%;
  background: linear-gradient(to top, #60a5fa, #3b82f6);
  border-radius: 2px;
  transition: height 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: absolute;
  bottom: 0;
  box-shadow: 0 0 10px rgba(96, 165, 250, 0.5);
}

.page-counter {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  background: rgba(0, 0, 0, 0.3);
  padding: 4px 8px;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-progress {
    right: 16px;
  }

  .progress-bar {
    height: 80px;
  }
}

.main-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}

.hero-section {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 24px;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.page-placeholder {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 24px;
}

.placeholder-title {
  font-size: clamp(2rem, 5vw, 4rem);
  font-weight: 700;
  margin-bottom: 24px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

.placeholder-text {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
  font-weight: 400;
  opacity: 0.9;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  line-height: 1.6;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    padding: 0 16px;
  }

  .main-content {
    background-attachment: scroll;
  }
}
</style>
